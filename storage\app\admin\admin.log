[2025-08-27 03:08:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:20:45] local.ERROR: {"query":[],"parameter":{"domainName":"intrusivetot.com","userEmail":"<EMAIL>","domainId":120,"createdDate":"2025-08-27 01:21:06","userID":7,"reason":"test est setst set set"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"created_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ...te\", \"support_note\", \"deleted_at\", \"is_refunded\", \"created_a...
                                                             ^ (Connection: client, SQL: insert into \"domain_cancellation_requests\" (\"user_id\", \"domain_id\", \"reason\", \"requested_at\", \"support_agent_id\", \"support_agent_name\", \"feedback_date\", \"support_note\", \"deleted_at\", \"is_refunded\", \"created_at\", \"updated_at\") values (7, 120, test est setst set set, 2025-08-27 03:20:45, 1, admin 1 (a@a.a), 2025-08-27 03:20:45, Request delete created by a@a.a, 2025-08-27 03:20:45, 0, 2025-08-27 03:20:45, 2025-08-27 03:20:45))","code":"42703"}  
[2025-08-27 03:21:00] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:21:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Processed 4 expired requests  
[2025-08-27 03:24:01] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 03:24:06] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:24:06] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:24:06] local.INFO: number of attempts: 1  
[2025-08-27 03:24:06] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:24:06] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-27 03:24:08] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:24:08] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:24:08] local.INFO: number of attempts: 1  
[2025-08-27 03:24:08] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:24:08] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Processed 2 expired requests  
[2025-08-27 03:25:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-27 03:25:04] local.INFO: Domain limittestme.com has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:25:04] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:25:04] local.INFO: number of attempts: 1  
[2025-08-27 03:25:04] local.ERROR: Domain limittestme.com has clientDeleteProhibited status  
[2025-08-27 03:25:04] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.com has clientDeleteProhibited status","code":0}  
[2025-08-27 03:25:07] local.INFO: Domain limittestme.net has clientDeleteProhibited status, cannot delete  
[2025-08-27 03:25:07] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:25:07] local.INFO: number of attempts: 1  
[2025-08-27 03:25:07] local.ERROR: Domain limittestme.net has clientDeleteProhibited status  
[2025-08-27 03:25:07] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain limittestme.net has clientDeleteProhibited status","code":0}  
[2025-08-27 05:02:13] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-08-27 05:02:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-27 05:03:42] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-08-27 05:10:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
